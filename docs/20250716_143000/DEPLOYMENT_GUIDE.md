# 部署指南

## 部署概述

知识库服务支持多种部署方式，包括本地部署、Docker部署和Kubernetes部署。本文档详细介绍各种部署方式的配置和操作步骤。

## 环境要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB可用空间
- **网络**: 稳定的网络连接

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 100GB SSD
- **网络**: 千兆网络

### 软件依赖
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **PostgreSQL**: 14+
- **pgvector**: 最新版本
- **Docker**: 20.10+ (Docker部署)
- **Kubernetes**: 1.20+ (K8s部署)

## 本地部署

### 1. 环境准备

#### 安装PostgreSQL和pgvector
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo apt install postgresql-14-pgvector

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo yum install pgvector

# 启动PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 配置数据库
```bash
# 切换到postgres用户
sudo -u postgres psql

-- 创建数据库和用户
CREATE DATABASE knowledge_server;
CREATE USER knowledge_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE knowledge_server TO knowledge_user;

-- 安装向量扩展
\c knowledge_server
CREATE EXTENSION vector;
\q
```

### 2. 应用部署

#### 下载和配置
```bash
# 下载应用程序
wget https://github.com/your-org/knowledge-server/releases/download/v1.0.0/knowledge-server-linux-amd64.tar.gz
tar -xzf knowledge-server-linux-amd64.tar.gz
cd knowledge-server

# 配置应用
cp configs/config.example.yaml configs/config.yaml
vim configs/config.yaml
```

#### 配置文件示例
```yaml
# configs/config.yaml
server:
  port: 8080
  mode: release
  read_timeout: 30s
  write_timeout: 30s

database:
  host: localhost
  port: 5432
  user: knowledge_user
  password: your_password
  dbname: knowledge_server
  sslmode: disable
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 3600
  conn_max_idle_time: 1800

embedding:
  provider: openai
  model: text-embedding-ada-002
  api_key: your_openai_api_key
  dimension: 1536
  timeout: 30s

logging:
  level: info
  format: json
  output: /var/log/knowledge-server/app.log

redis:
  addr: localhost:6379
  password: ""
  db: 0
  pool_size: 10
```

#### 运行数据库迁移
```bash
# 运行迁移
./knowledge-server migrate up

# 验证迁移
./knowledge-server migrate version
```

#### 启动应用
```bash
# 前台运行
./knowledge-server

# 后台运行
nohup ./knowledge-server > /var/log/knowledge-server/app.log 2>&1 &
```

### 3. 系统服务配置

#### 创建systemd服务
```bash
# 创建服务文件
sudo vim /etc/systemd/system/knowledge-server.service
```

```ini
[Unit]
Description=Knowledge Server
After=network.target postgresql.service

[Service]
Type=simple
User=knowledge
Group=knowledge
WorkingDirectory=/opt/knowledge-server
ExecStart=/opt/knowledge-server/knowledge-server
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=knowledge-server

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start knowledge-server

# 设置开机自启
sudo systemctl enable knowledge-server

# 查看服务状态
sudo systemctl status knowledge-server
```

## Docker部署

### 1. Dockerfile

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o knowledge-server cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/knowledge-server .
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/migrations ./migrations

EXPOSE 8080
CMD ["./knowledge-server"]
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  knowledge-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=knowledge_user
      - DB_PASSWORD=knowledge_pass
      - DB_NAME=knowledge_server
      - REDIS_ADDR=redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./configs:/root/configs
      - ./logs:/var/log/knowledge-server
    restart: unless-stopped

  postgres:
    image: pgvector/pgvector:pg14
    environment:
      POSTGRES_DB: knowledge_server
      POSTGRES_USER: knowledge_user
      POSTGRES_PASSWORD: knowledge_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U knowledge_user -d knowledge_server"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - knowledge-server
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 3. 部署命令

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f knowledge-server

# 停止服务
docker-compose down

# 更新服务
docker-compose pull
docker-compose up -d --force-recreate
```

## Kubernetes部署

### 1. 命名空间和配置

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: knowledge-server
---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: knowledge-server-config
  namespace: knowledge-server
data:
  config.yaml: |
    server:
      port: 8080
      mode: release
    database:
      host: postgres-service
      port: 5432
      user: knowledge_user
      password: knowledge_pass
      dbname: knowledge_server
    embedding:
      provider: openai
      model: text-embedding-ada-002
      api_key: ${OPENAI_API_KEY}
    redis:
      addr: redis-service:6379
---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: knowledge-server-secret
  namespace: knowledge-server
type: Opaque
data:
  openai-api-key: <base64-encoded-api-key>
  db-password: <base64-encoded-password>
```

### 2. 数据库部署

```yaml
# postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: knowledge-server
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: pgvector/pgvector:pg14
        env:
        - name: POSTGRES_DB
          value: knowledge_server
        - name: POSTGRES_USER
          value: knowledge_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: knowledge-server-secret
              key: db-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: knowledge-server
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
```

### 3. 应用部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-server
  namespace: knowledge-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: knowledge-server
  template:
    metadata:
      labels:
        app: knowledge-server
    spec:
      containers:
      - name: knowledge-server
        image: knowledge-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: knowledge-server-secret
              key: openai-api-key
        volumeMounts:
        - name: config-volume
          mountPath: /root/configs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: knowledge-server-config
---
apiVersion: v1
kind: Service
metadata:
  name: knowledge-server-service
  namespace: knowledge-server
spec:
  selector:
    app: knowledge-server
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: knowledge-server-ingress
  namespace: knowledge-server
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.knowledge-server.com
    secretName: knowledge-server-tls
  rules:
  - host: api.knowledge-server.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: knowledge-server-service
            port:
              number: 80
```

### 4. 部署命令

```bash
# 应用配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml

# 部署数据库
kubectl apply -f postgres.yaml

# 部署应用
kubectl apply -f deployment.yaml

# 查看状态
kubectl get pods -n knowledge-server
kubectl get services -n knowledge-server

# 查看日志
kubectl logs -f deployment/knowledge-server -n knowledge-server
```

## 负载均衡和反向代理

### 1. Nginx配置

```nginx
# nginx.conf
upstream knowledge_server {
    server 127.0.0.1:8080;
    # 如果有多个实例
    # server 127.0.0.1:8081;
    # server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name api.knowledge-server.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.knowledge-server.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    client_max_body_size 100M;

    location / {
        proxy_pass http://knowledge_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /api/v1/knowledge-bases/*/documents {
        proxy_pass http://knowledge_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传特殊配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        client_max_body_size 500M;
    }
}
```

## 监控和日志

### 1. 日志配置

```yaml
# 应用日志配置
logging:
  level: info
  format: json
  output: /var/log/knowledge-server/app.log
  max_size: 100MB
  max_backups: 10
  max_age: 30
  compress: true
```

### 2. 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'knowledge-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: /metrics
    scrape_interval: 10s
```

## 备份和恢复

### 1. 数据库备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/knowledge-server"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="knowledge_server"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U knowledge_user -d $DB_NAME \
    --format=custom --compress=9 \
    --file=$BACKUP_DIR/db_backup_$DATE.dump

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "db_backup_*.dump" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/db_backup_$DATE.dump"
```

### 2. 应用备份

```bash
#!/bin/bash
# app_backup.sh

APP_DIR="/opt/knowledge-server"
BACKUP_DIR="/backup/knowledge-server"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份配置文件
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz -C $APP_DIR configs/

# 备份日志文件
tar -czf $BACKUP_DIR/logs_backup_$DATE.tar.gz /var/log/knowledge-server/

echo "应用备份完成"
```

## 故障排除

### 1. 常见问题

#### 数据库连接失败
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 检查连接
psql -h localhost -U knowledge_user -d knowledge_server

# 查看日志
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

#### 应用启动失败
```bash
# 查看应用日志
sudo journalctl -u knowledge-server -f

# 检查配置文件
./knowledge-server config validate

# 检查端口占用
sudo netstat -tlnp | grep 8080
```

### 2. 性能调优

#### 数据库优化
```sql
-- 查看慢查询
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- 分析表统计信息
ANALYZE documents;
ANALYZE document_chunks;
```

#### 应用优化
```bash
# 查看内存使用
ps aux | grep knowledge-server

# 查看连接数
ss -tuln | grep 8080
```
