# 开发指南

## 开发环境搭建

### 1. 前置要求

- **Go**: 1.21或更高版本
- **PostgreSQL**: 14或更高版本
- **pgvector**: PostgreSQL向量扩展
- **Git**: 版本控制
- **Make**: 构建工具
- **Docker**: 容器化部署（可选）

### 2. 环境安装

#### 安装Go
```bash
# macOS
brew install go

# Ubuntu/Debian
sudo apt update
sudo apt install golang-go

# 验证安装
go version
```

#### 安装PostgreSQL和pgvector
```bash
# macOS
brew install postgresql
brew install pgvector

# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib
sudo apt install postgresql-14-pgvector

# 启动PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 配置数据库
```bash
# 创建数据库用户
sudo -u postgres createuser --interactive

# 创建数据库
sudo -u postgres createdb knowledge_server

# 连接数据库并安装扩展
sudo -u postgres psql knowledge_server
CREATE EXTENSION vector;
\q
```

### 3. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd knowledge-server

# 安装依赖
go mod tidy

# 安装开发工具
go install github.com/google/wire/cmd/wire@latest
go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 生成依赖注入代码
wire ./...

# 复制配置文件
cp configs/config.example.yaml configs/config.yaml
```

## 项目结构详解

```
knowledge-server/
├── cmd/                    # 应用程序入口
│   └── server/
│       ├── main.go        # 主程序入口
│       └── wire.go        # Wire依赖注入配置
├── internal/              # 内部包（不对外暴露）
│   ├── api/              # API层
│   │   ├── handler/      # HTTP处理器
│   │   │   ├── knowledge.go
│   │   │   ├── document.go
│   │   │   └── search.go
│   │   ├── middleware/   # 中间件
│   │   │   ├── auth.go
│   │   │   ├── cors.go
│   │   │   ├── logger.go
│   │   │   └── ratelimit.go
│   │   └── router/       # 路由配置
│   │       └── router.go
│   ├── service/          # 服务层
│   │   ├── knowledge/
│   │   │   ├── service.go
│   │   │   └── dto.go
│   │   └── vector/
│   │       ├── service.go
│   │       └── dto.go
│   ├── biz/              # 业务层
│   │   ├── knowledge/
│   │   │   ├── biz.go
│   │   │   ├── entity.go
│   │   │   └── repository.go
│   │   └── vector/
│   │       ├── biz.go
│   │       ├── entity.go
│   │       └── repository.go
│   └── data/             # 数据层
│       ├── database/
│       │   └── postgres.go
│       ├── repository/
│       │   ├── knowledge.go
│       │   └── document.go
│       └── model/
│           ├── knowledge.go
│           └── document.go
├── pkg/                  # 公共包（可对外暴露）
│   ├── config/
│   │   └── config.go
│   ├── logger/
│   │   └── logger.go
│   ├── embedding/
│   │   ├── interface.go
│   │   ├── openai.go
│   │   └── factory.go
│   └── utils/
│       ├── hash.go
│       └── validator.go
├── configs/              # 配置文件
│   ├── config.yaml
│   └── config.example.yaml
├── migrations/           # 数据库迁移文件
│   ├── 001_create_users.up.sql
│   ├── 001_create_users.down.sql
│   └── ...
├── scripts/              # 脚本文件
│   ├── build.sh
│   ├── test.sh
│   └── deploy.sh
├── docs/                 # 文档
├── Makefile             # 构建配置
├── Dockerfile           # Docker配置
├── docker-compose.yml   # Docker Compose配置
├── go.mod              # Go模块定义
└── go.sum              # Go模块校验
```

## 编码规范

### 1. 命名规范

#### 包命名
- 使用小写字母
- 简短且有意义
- 避免使用下划线

```go
// 好的例子
package knowledge
package vector
package config

// 不好的例子
package knowledge_base
package vectorService
```

#### 变量和函数命名
- 使用驼峰命名法
- 导出的标识符首字母大写
- 私有标识符首字母小写

```go
// 好的例子
type KnowledgeBase struct {
    ID   int64  `json:"id"`
    Name string `json:"name"`
}

func CreateKnowledgeBase(ctx context.Context, req *CreateRequest) error {
    // ...
}

// 私有函数
func validateRequest(req *CreateRequest) error {
    // ...
}
```

#### 常量命名
- 使用全大写字母
- 单词间用下划线分隔

```go
const (
    STATUS_ACTIVE   = 1
    STATUS_INACTIVE = 0
    STATUS_DELETED  = -1
)
```

### 2. 代码组织

#### 文件结构
每个文件应该有清晰的职责：

```go
// knowledge.go
package knowledge

import (
    // 标准库
    "context"
    "fmt"
    
    // 第三方库
    "github.com/gin-gonic/gin"
    
    // 项目内部包
    "knowledge-server/internal/biz/knowledge"
    "knowledge-server/pkg/logger"
)

// 类型定义
type Handler struct {
    knowledgeBiz knowledge.Biz
    logger       logger.Logger
}

// 构造函数
func NewHandler(knowledgeBiz knowledge.Biz, logger logger.Logger) *Handler {
    return &Handler{
        knowledgeBiz: knowledgeBiz,
        logger:       logger,
    }
}

// 方法实现
func (h *Handler) CreateKnowledgeBase(c *gin.Context) {
    // 实现逻辑
}
```

#### 接口设计
- 接口应该小而专注
- 使用组合而不是继承

```go
// 好的例子
type Reader interface {
    Read(ctx context.Context, id int64) (*KnowledgeBase, error)
}

type Writer interface {
    Create(ctx context.Context, kb *KnowledgeBase) error
    Update(ctx context.Context, kb *KnowledgeBase) error
}

type Repository interface {
    Reader
    Writer
}

// 不好的例子
type Repository interface {
    Create(ctx context.Context, kb *KnowledgeBase) error
    Read(ctx context.Context, id int64) (*KnowledgeBase, error)
    Update(ctx context.Context, kb *KnowledgeBase) error
    Delete(ctx context.Context, id int64) error
    List(ctx context.Context, req *ListRequest) ([]*KnowledgeBase, error)
    Search(ctx context.Context, keyword string) ([]*KnowledgeBase, error)
    // ... 更多方法
}
```

### 3. 错误处理

#### 错误定义
```go
// pkg/errors/errors.go
package errors

import "fmt"

type ErrorCode int

const (
    ErrCodeValidation ErrorCode = 1001
    ErrCodeNotFound   ErrorCode = 3001
    ErrCodeInternal   ErrorCode = 5001
)

type AppError struct {
    Code    ErrorCode `json:"code"`
    Message string    `json:"message"`
    Err     error     `json:"-"`
}

func (e *AppError) Error() string {
    if e.Err != nil {
        return fmt.Sprintf("code: %d, message: %s, error: %v", e.Code, e.Message, e.Err)
    }
    return fmt.Sprintf("code: %d, message: %s", e.Code, e.Message)
}

func NewValidationError(message string) *AppError {
    return &AppError{
        Code:    ErrCodeValidation,
        Message: message,
    }
}
```

#### 错误处理
```go
// 业务层错误处理
func (b *knowledgeBiz) CreateKnowledgeBase(ctx context.Context, req *CreateRequest) (*KnowledgeBase, error) {
    // 参数验证
    if err := b.validateCreateRequest(req); err != nil {
        return nil, errors.NewValidationError("参数验证失败: " + err.Error())
    }
    
    // 业务逻辑
    kb := &KnowledgeBase{
        Name:        req.Name,
        Description: req.Description,
    }
    
    if err := b.repo.Create(ctx, kb); err != nil {
        b.logger.Error("创建知识库失败", "error", err)
        return nil, errors.NewInternalError("创建知识库失败")
    }
    
    return kb, nil
}
```

### 4. 日志规范

```go
// 使用结构化日志
logger.Info("创建知识库", 
    "user_id", userID,
    "knowledge_base_name", req.Name,
    "duration", time.Since(start),
)

logger.Error("数据库操作失败",
    "operation", "create_knowledge_base",
    "error", err,
    "user_id", userID,
)
```

## 测试指南

### 1. 单元测试

#### 测试文件命名
- 测试文件以`_test.go`结尾
- 测试函数以`Test`开头

```go
// knowledge_test.go
package knowledge

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

func TestKnowledgeBiz_CreateKnowledgeBase(t *testing.T) {
    // 准备测试数据
    req := &CreateRequest{
        Name:        "测试知识库",
        Description: "测试描述",
    }
    
    // 创建mock
    mockRepo := &MockRepository{}
    mockRepo.On("Create", mock.Anything, mock.Anything).Return(nil)
    
    // 创建业务对象
    biz := NewKnowledgeBiz(mockRepo, logger.NewNopLogger())
    
    // 执行测试
    kb, err := biz.CreateKnowledgeBase(context.Background(), req)
    
    // 断言结果
    assert.NoError(t, err)
    assert.NotNil(t, kb)
    assert.Equal(t, req.Name, kb.Name)
    
    // 验证mock调用
    mockRepo.AssertExpectations(t)
}
```

#### Mock生成
使用mockery生成mock文件：

```bash
# 安装mockery
go install github.com/vektra/mockery/v2@latest

# 生成mock
mockery --name=Repository --dir=internal/biz/knowledge --output=internal/biz/knowledge/mocks
```

### 2. 集成测试

```go
// integration_test.go
//go:build integration

package integration

import (
    "testing"
    "knowledge-server/internal/data"
    "knowledge-server/pkg/config"
)

func TestKnowledgeRepository_Integration(t *testing.T) {
    // 设置测试数据库
    cfg := config.LoadTestConfig()
    db := data.NewTestDatabase(cfg)
    defer db.Close()
    
    // 运行迁移
    runMigrations(db)
    
    // 创建repository
    repo := data.NewKnowledgeRepository(db)
    
    // 执行测试
    // ...
}
```

### 3. API测试

```go
// api_test.go
func TestKnowledgeHandler_CreateKnowledgeBase(t *testing.T) {
    // 设置测试服务器
    router := setupTestRouter()
    
    // 准备请求数据
    reqBody := `{
        "name": "测试知识库",
        "description": "测试描述"
    }`
    
    // 发送请求
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/api/v1/knowledge-bases", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+testToken)
    
    router.ServeHTTP(w, req)
    
    // 验证响应
    assert.Equal(t, http.StatusCreated, w.Code)
    
    var resp map[string]interface{}
    err := json.Unmarshal(w.Body.Bytes(), &resp)
    assert.NoError(t, err)
    assert.Equal(t, float64(0), resp["code"])
}
```

## 构建和部署

### 1. Makefile

```makefile
# Makefile
.PHONY: build test clean wire migrate

# 变量定义
APP_NAME=knowledge-server
VERSION=$(shell git describe --tags --always)
BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GO_VERSION=$(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 默认目标
all: wire build

# 生成依赖注入代码
wire:
	wire ./...

# 构建
build: wire
	go build $(LDFLAGS) -o bin/$(APP_NAME) cmd/server/main.go

# 测试
test:
	go test -v ./...

# 集成测试
test-integration:
	go test -v -tags=integration ./...

# 代码检查
lint:
	golangci-lint run

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://user:password@localhost/knowledge_server?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://user:password@localhost/knowledge_server?sslmode=disable" down

# 清理
clean:
	rm -rf bin/
	go clean

# 运行
run: build
	./bin/$(APP_NAME)

# Docker构建
docker-build:
	docker build -t $(APP_NAME):$(VERSION) .

# Docker运行
docker-run:
	docker-compose up -d
```

### 2. 持续集成

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: pgvector/pgvector:pg14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: knowledge_server_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: Install dependencies
      run: |
        go mod download
        go install github.com/google/wire/cmd/wire@latest
    
    - name: Generate code
      run: wire ./...
    
    - name: Run tests
      run: go test -v ./...
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_NAME: knowledge_server_test
    
    - name: Run linter
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
```

## 性能优化

### 1. 数据库优化
- 使用连接池
- 添加适当的索引
- 优化查询语句
- 使用预编译语句

### 2. 缓存策略
- Redis缓存热点数据
- 本地缓存计算结果
- 合理设置缓存过期时间

### 3. 并发优化
- 使用Goroutine池
- 避免过度并发
- 合理使用锁机制

### 4. 内存优化
- 及时释放资源
- 使用对象池
- 避免内存泄漏

## 监控和调试

### 1. 性能监控
```go
// 添加性能监控中间件
func PerformanceMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        logger.Info("请求处理完成",
            "method", c.Request.Method,
            "path", c.Request.URL.Path,
            "status", c.Writer.Status(),
            "duration", duration,
        )
    }
}
```

### 2. 健康检查
```go
func (h *HealthHandler) Check(c *gin.Context) {
    status := map[string]string{
        "database": h.checkDatabase(),
        "embedding": h.checkEmbedding(),
    }
    
    c.JSON(http.StatusOK, gin.H{
        "status": "healthy",
        "services": status,
        "timestamp": time.Now(),
    })
}
```
