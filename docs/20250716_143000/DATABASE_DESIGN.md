# 数据库设计文档

## 概述

知识库服务使用PostgreSQL作为主数据库，并集成pgvector扩展来支持向量存储和检索功能。数据库设计遵循第三范式，确保数据一致性和查询效率。

## 数据库架构

```
PostgreSQL + pgvector
├── 业务数据存储 (关系型数据)
├── 向量数据存储 (向量索引)
├── 全文检索支持 (GIN索引)
└── 混合检索能力 (关系+向量)
```

## 核心表设计

### 1. knowledge_bases (知识库表)

存储知识库的基本信息和配置。

```sql
CREATE TABLE knowledge_bases (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    settings JSONB DEFAULT '{}',
    status INTEGER DEFAULT 1, -- 1:active, 0:inactive, -1:deleted
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_knowledge_bases_name ON knowledge_bases(name);
CREATE INDEX idx_knowledge_bases_status ON knowledge_bases(status);
CREATE INDEX idx_knowledge_bases_created_by ON knowledge_bases(created_by);
CREATE INDEX idx_knowledge_bases_created_at ON knowledge_bases(created_at);
```

**字段说明**:
- `id`: 主键，自增长整型
- `name`: 知识库名称，唯一约束
- `description`: 知识库描述
- `settings`: 知识库配置信息(JSON格式)
- `status`: 状态标识
- `created_by`: 创建者ID
- `created_at/updated_at`: 时间戳
- `deleted_at`: 软删除时间戳

### 2. documents (文档表)

存储知识库中的文档内容和元数据。

```sql
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    knowledge_base_id BIGINT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL, -- SHA256 hash for deduplication
    file_path VARCHAR(1000),
    file_size BIGINT DEFAULT 0,
    file_type VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    status INTEGER DEFAULT 1, -- 1:active, 0:processing, -1:failed
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_documents_kb_id ON documents(knowledge_base_id);
CREATE INDEX idx_documents_title ON documents USING GIN(to_tsvector('english', title));
CREATE INDEX idx_documents_content ON documents USING GIN(to_tsvector('english', content));
CREATE INDEX idx_documents_hash ON documents(content_hash);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_created_at ON documents(created_at);

-- 复合索引
CREATE INDEX idx_documents_kb_status ON documents(knowledge_base_id, status);
```

**字段说明**:
- `knowledge_base_id`: 关联的知识库ID
- `title`: 文档标题
- `content`: 文档内容
- `content_hash`: 内容哈希值，用于去重
- `file_path`: 原始文件路径
- `file_size`: 文件大小
- `file_type`: 文件类型
- `metadata`: 文档元数据

### 3. document_chunks (文档分块表)

存储文档分块信息，用于向量化和检索。

```sql
CREATE TABLE document_chunks (
    id BIGSERIAL PRIMARY KEY,
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_length INTEGER NOT NULL,
    embedding VECTOR(1536), -- 向量维度，根据embedding模型调整
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_chunks_doc_index ON document_chunks(document_id, chunk_index);
CREATE INDEX idx_chunks_content ON document_chunks USING GIN(to_tsvector('english', content));

-- 向量索引 (HNSW算法，适合高维向量相似度搜索)
CREATE INDEX idx_chunks_embedding ON document_chunks 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- IVFFlat索引 (适合大规模数据)
-- CREATE INDEX idx_chunks_embedding_ivf ON document_chunks 
-- USING ivfflat (embedding vector_cosine_ops) 
-- WITH (lists = 100);
```

**字段说明**:
- `document_id`: 关联的文档ID
- `chunk_index`: 分块在文档中的序号
- `content`: 分块内容
- `content_length`: 分块内容长度
- `embedding`: 分块的向量表示
- `metadata`: 分块元数据

### 4. search_logs (搜索日志表)

记录用户的搜索行为，用于分析和优化。

```sql
CREATE TABLE search_logs (
    id BIGSERIAL PRIMARY KEY,
    knowledge_base_id BIGINT REFERENCES knowledge_bases(id),
    query_text TEXT NOT NULL,
    query_embedding VECTOR(1536),
    search_type VARCHAR(50) NOT NULL, -- 'semantic', 'keyword', 'hybrid'
    filters JSONB DEFAULT '{}',
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    user_id BIGINT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_search_logs_kb_id ON search_logs(knowledge_base_id);
CREATE INDEX idx_search_logs_user_id ON search_logs(user_id);
CREATE INDEX idx_search_logs_created_at ON search_logs(created_at);
CREATE INDEX idx_search_logs_search_type ON search_logs(search_type);

-- 分区表 (按月分区，提高查询性能)
CREATE TABLE search_logs_y2024m01 PARTITION OF search_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 5. users (用户表)

存储用户基本信息。

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(200),
    avatar_url VARCHAR(500),
    role VARCHAR(50) DEFAULT 'user', -- 'admin', 'user', 'readonly'
    status INTEGER DEFAULT 1, -- 1:active, 0:inactive, -1:banned
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE UNIQUE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
```

### 6. knowledge_base_permissions (知识库权限表)

管理用户对知识库的访问权限。

```sql
CREATE TABLE knowledge_base_permissions (
    id BIGSERIAL PRIMARY KEY,
    knowledge_base_id BIGINT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission VARCHAR(50) NOT NULL, -- 'read', 'write', 'admin'
    granted_by BIGINT REFERENCES users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(knowledge_base_id, user_id)
);

-- 索引
CREATE INDEX idx_kb_permissions_kb_id ON knowledge_base_permissions(knowledge_base_id);
CREATE INDEX idx_kb_permissions_user_id ON knowledge_base_permissions(user_id);
CREATE INDEX idx_kb_permissions_permission ON knowledge_base_permissions(permission);
```

## 向量检索优化

### 1. 向量索引策略

```sql
-- HNSW索引：适合实时查询，内存占用较高
CREATE INDEX idx_chunks_embedding_hnsw ON document_chunks 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- IVFFlat索引：适合大规模数据，查询速度稍慢但内存占用少
CREATE INDEX idx_chunks_embedding_ivf ON document_chunks 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

### 2. 相似度搜索查询

```sql
-- 余弦相似度搜索
SELECT 
    dc.id,
    dc.content,
    d.title,
    1 - (dc.embedding <=> $1::vector) as similarity
FROM document_chunks dc
JOIN documents d ON dc.document_id = d.id
WHERE d.knowledge_base_id = $2
    AND d.status = 1
ORDER BY dc.embedding <=> $1::vector
LIMIT $3;

-- 混合搜索 (向量 + 关键词)
SELECT 
    dc.id,
    dc.content,
    d.title,
    (1 - (dc.embedding <=> $1::vector)) * 0.7 + 
    ts_rank(to_tsvector('english', dc.content), plainto_tsquery('english', $2)) * 0.3 as score
FROM document_chunks dc
JOIN documents d ON dc.document_id = d.id
WHERE d.knowledge_base_id = $3
    AND d.status = 1
    AND (
        dc.embedding <=> $1::vector < 0.5 
        OR to_tsvector('english', dc.content) @@ plainto_tsquery('english', $2)
    )
ORDER BY score DESC
LIMIT $4;
```

## 数据库配置优化

### 1. PostgreSQL配置

```ini
# postgresql.conf

# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 向量扩展配置
shared_preload_libraries = 'vector'

# 连接配置
max_connections = 100
listen_addresses = '*'

# 日志配置
log_statement = 'mod'
log_min_duration_statement = 1000

# 检查点配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
```

### 2. 连接池配置

```go
// 数据库连接池配置
type DatabaseConfig struct {
    Host            string `yaml:"host"`
    Port            int    `yaml:"port"`
    User            string `yaml:"user"`
    Password        string `yaml:"password"`
    DBName          string `yaml:"dbname"`
    SSLMode         string `yaml:"sslmode"`
    MaxOpenConns    int    `yaml:"max_open_conns"`    // 最大打开连接数
    MaxIdleConns    int    `yaml:"max_idle_conns"`    // 最大空闲连接数
    ConnMaxLifetime int    `yaml:"conn_max_lifetime"` // 连接最大生存时间(秒)
    ConnMaxIdleTime int    `yaml:"conn_max_idle_time"` // 连接最大空闲时间(秒)
}
```
```

## 数据迁移和版本管理

### 1. 迁移脚本结构

```
migrations/
├── 001_create_users_table.up.sql
├── 001_create_users_table.down.sql
├── 002_create_knowledge_bases_table.up.sql
├── 002_create_knowledge_bases_table.down.sql
├── 003_create_documents_table.up.sql
├── 003_create_documents_table.down.sql
├── 004_create_document_chunks_table.up.sql
├── 004_create_document_chunks_table.down.sql
└── 005_create_search_logs_table.up.sql
```

### 2. 初始化脚本

```sql
-- init.sql
-- 创建数据库
CREATE DATABASE knowledge_server;

-- 连接到数据库
\c knowledge_server;

-- 安装向量扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建全文搜索配置
CREATE TEXT SEARCH CONFIGURATION chinese (COPY = simple);
```

## 备份和恢复策略

### 1. 备份策略

```bash
#!/bin/bash
# backup.sh

# 全量备份
pg_dump -h localhost -U postgres -d knowledge_server \
    --format=custom --compress=9 \
    --file=backup_$(date +%Y%m%d_%H%M%S).dump

# 增量备份 (WAL归档)
archive_command = 'cp %p /backup/wal_archive/%f'
```

### 2. 恢复策略

```bash
#!/bin/bash
# restore.sh

# 从备份恢复
pg_restore -h localhost -U postgres -d knowledge_server_new \
    --clean --if-exists backup_20240716_143000.dump
```

## 监控和维护

### 1. 性能监控查询

```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 查看慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### 2. 维护任务

```sql
-- 定期清理和分析
VACUUM ANALYZE documents;
VACUUM ANALYZE document_chunks;

-- 重建索引
REINDEX INDEX idx_chunks_embedding;

-- 清理过期日志
DELETE FROM search_logs 
WHERE created_at < NOW() - INTERVAL '90 days';
```
