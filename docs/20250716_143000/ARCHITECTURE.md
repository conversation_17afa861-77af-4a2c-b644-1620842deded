# 架构设计文档

## 整体架构

知识库服务采用分层架构设计，遵循依赖倒置原则，确保各层职责清晰，便于测试和维护。

```
┌─────────────────────────────────────────────────────────────┐
│                        API Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Handler   │  │ Middleware  │  │   Router    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      Service Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Knowledge   │  │   Vector    │  │   Common    │         │
│  │  Service    │  │  Service    │  │  Service    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Business Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Knowledge   │  │   Vector    │  │ Embedding   │         │
│  │    Biz      │  │    Biz      │  │    Biz      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                       Data Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Repository  │  │   Database  │  │    Model    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 分层职责

### 1. API Layer (API层)
**职责**: HTTP请求处理、参数验证、响应格式化

**组件**:
- **Handler**: 处理HTTP请求，调用Service层
- **Middleware**: 认证、日志、限流等横切关注点
- **Router**: 路由配置和管理

**设计原则**:
- 只处理HTTP协议相关逻辑
- 不包含业务逻辑
- 统一错误处理和响应格式

### 2. Service Layer (服务层)
**职责**: 服务编排、事务管理、外部服务调用

**组件**:
- **KnowledgeService**: 知识库相关服务编排
- **VectorService**: 向量处理服务编排
- **CommonService**: 通用服务功能

**设计原则**:
- 编排多个Business层组件
- 管理事务边界
- 处理外部服务集成

### 3. Business Layer (业务层)
**职责**: 核心业务逻辑、领域规则、业务验证

**组件**:
- **KnowledgeBiz**: 知识库业务逻辑
- **VectorBiz**: 向量处理业务逻辑
- **EmbeddingBiz**: 文本向量化业务逻辑

**设计原则**:
- 包含核心业务规则
- 独立于外部依赖
- 高内聚、低耦合

### 4. Data Layer (数据层)
**职责**: 数据持久化、数据访问、数据模型

**组件**:
- **Repository**: 数据访问接口实现
- **Database**: 数据库连接和操作
- **Model**: 数据模型定义

**设计原则**:
- 封装数据访问细节
- 提供统一的数据接口
- 支持多种数据源

## 依赖注入设计

使用Wire进行依赖注入，确保组件间的松耦合和可测试性。

### Wire配置结构

```go
// wire.go
//go:build wireinject

package main

import (
    "github.com/google/wire"
    "knowledge-server/internal/api"
    "knowledge-server/internal/service"
    "knowledge-server/internal/biz"
    "knowledge-server/internal/data"
    "knowledge-server/pkg/config"
    "knowledge-server/pkg/logger"
)

// 应用程序依赖集合
var AppSet = wire.NewSet(
    // 配置和基础设施
    config.NewConfig,
    logger.NewLogger,
    
    // 数据层
    data.NewDatabase,
    data.NewKnowledgeRepository,
    data.NewDocumentRepository,
    
    // 业务层
    biz.NewKnowledgeBiz,
    biz.NewVectorBiz,
    biz.NewEmbeddingBiz,
    
    // 服务层
    service.NewKnowledgeService,
    service.NewVectorService,
    
    // API层
    api.NewKnowledgeHandler,
    api.NewVectorHandler,
    api.NewRouter,
)

func InitializeApp() (*api.Server, error) {
    wire.Build(AppSet, api.NewServer)
    return nil, nil
}
```

### 接口设计

每层都定义清晰的接口，便于测试和替换实现：

```go
// 业务层接口
type KnowledgeBiz interface {
    CreateKnowledgeBase(ctx context.Context, req *CreateKnowledgeBaseRequest) (*KnowledgeBase, error)
    GetKnowledgeBase(ctx context.Context, id int64) (*KnowledgeBase, error)
    // ...
}

// 数据层接口
type KnowledgeRepository interface {
    Create(ctx context.Context, kb *KnowledgeBase) error
    GetByID(ctx context.Context, id int64) (*KnowledgeBase, error)
    // ...
}
```

## 模块化设计

### 核心模块

#### 1. Knowledge Module (知识库模块)
```
internal/
├── api/handler/knowledge.go
├── service/knowledge/
│   ├── service.go
│   └── dto.go
├── biz/knowledge/
│   ├── biz.go
│   ├── entity.go
│   └── repository.go
└── data/repository/knowledge.go
```

#### 2. Vector Module (向量模块)
```
internal/
├── api/handler/vector.go
├── service/vector/
│   ├── service.go
│   └── dto.go
├── biz/vector/
│   ├── biz.go
│   ├── entity.go
│   └── repository.go
└── data/repository/vector.go
```

#### 3. Embedding Module (向量化模块)
```
pkg/embedding/
├── provider/
│   ├── openai.go
│   ├── huggingface.go
│   └── local.go
├── interface.go
└── factory.go
```

## 数据流设计

### 请求处理流程

```
HTTP Request
     │
     ▼
┌─────────────┐
│   Handler   │ ── 参数验证、格式转换
└─────────────┘
     │
     ▼
┌─────────────┐
│   Service   │ ── 服务编排、事务管理
└─────────────┘
     │
     ▼
┌─────────────┐
│  Business   │ ── 业务逻辑、规则验证
└─────────────┘
     │
     ▼
┌─────────────┐
│    Data     │ ── 数据持久化
└─────────────┘
     │
     ▼
   Database
```

### 向量检索流程

```
Search Request
     │
     ▼
┌─────────────────┐
│ Vector Handler  │ ── 接收检索请求
└─────────────────┘
     │
     ▼
┌─────────────────┐
│ Vector Service  │ ── 编排检索流程
└─────────────────┘
     │
     ▼
┌─────────────────┐
│ Embedding Biz   │ ── 查询文本向量化
└─────────────────┘
     │
     ▼
┌─────────────────┐
│  Vector Biz     │ ── 相似度计算
└─────────────────┘
     │
     ▼
┌─────────────────┐
│ Vector Repo     │ ── 向量数据库查询
└─────────────────┘
     │
     ▼
   Search Results
```

## 错误处理设计

### 错误分层

```go
// 定义错误类型
type ErrorCode int

const (
    ErrCodeValidation ErrorCode = 1001  // 参数验证错误
    ErrCodeBusiness   ErrorCode = 2001  // 业务逻辑错误
    ErrCodeData       ErrorCode = 3001  // 数据访问错误
    ErrCodeExternal   ErrorCode = 4001  // 外部服务错误
)

// 统一错误结构
type AppError struct {
    Code    ErrorCode `json:"code"`
    Message string    `json:"message"`
    Details string    `json:"details,omitempty"`
}
```

### 错误传播

- API层：HTTP状态码映射
- Service层：错误聚合和转换
- Business层：业务错误定义
- Data层：数据访问错误包装

## 性能优化设计

### 1. 数据库优化
- 连接池配置
- 索引优化
- 查询优化

### 2. 缓存策略
- Redis缓存热点数据
- 本地缓存向量计算结果
- 分层缓存策略

### 3. 并发处理
- Goroutine池
- 异步处理
- 批量操作

## 监控和可观测性

### 1. 日志设计
- 结构化日志
- 分层日志记录
- 链路追踪

### 2. 指标监控
- 业务指标
- 性能指标
- 错误指标

### 3. 健康检查
- 数据库连接检查
- 外部服务检查
- 系统资源检查

## 安全设计

### 1. 认证授权
- JWT Token认证
- RBAC权限控制
- API密钥管理

### 2. 数据安全
- 敏感数据加密
- SQL注入防护
- 输入验证

### 3. 网络安全
- HTTPS强制
- CORS配置
- 限流防护
