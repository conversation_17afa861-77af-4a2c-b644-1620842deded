# API设计文档

## 概述

知识库服务提供RESTful API，支持知识库管理、文档管理和向量检索功能。API设计遵循REST规范，使用JSON格式进行数据交换。

## API规范

### 1. 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **认证方式**: <PERSON><PERSON> (JWT)

### 2. 通用响应格式

#### 成功响应
```json
{
    "code": 0,
    "message": "success",
    "data": {
        // 具体数据
    },
    "timestamp": "2024-07-16T14:30:00Z"
}
```

#### 错误响应
```json
{
    "code": 1001,
    "message": "参数验证失败",
    "error": "name字段不能为空",
    "timestamp": "2024-07-16T14:30:00Z"
}
```

### 3. 状态码规范

| HTTP状态码 | 说明 | 使用场景 |
|-----------|------|----------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证 |
| 403 | Forbidden | 无权限 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 500 | Internal Server Error | 服务器内部错误 |

## 认证授权

### 1. 用户认证

#### 登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "password123"
}
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        }
    }
}
```

#### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <token>
```

### 2. 权限验证

所有需要认证的接口都需要在请求头中携带Token：

```http
Authorization: Bearer <token>
```

## 知识库管理API

### 1. 创建知识库

```http
POST /api/v1/knowledge-bases
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "技术文档库",
    "description": "存储技术相关文档",
    "settings": {
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "embedding_model": "text-embedding-ada-002"
    }
}
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "name": "技术文档库",
        "description": "存储技术相关文档",
        "settings": {
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "embedding_model": "text-embedding-ada-002"
        },
        "status": 1,
        "created_at": "2024-07-16T14:30:00Z",
        "updated_at": "2024-07-16T14:30:00Z"
    }
}
```

### 2. 获取知识库列表

```http
GET /api/v1/knowledge-bases?page=1&size=10&keyword=技术
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10，最大100
- `keyword`: 搜索关键词（可选）
- `status`: 状态过滤（可选）

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "技术文档库",
                "description": "存储技术相关文档",
                "document_count": 25,
                "status": 1,
                "created_at": "2024-07-16T14:30:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 10,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 3. 获取知识库详情

```http
GET /api/v1/knowledge-bases/{id}
Authorization: Bearer <token>
```

### 4. 更新知识库

```http
PUT /api/v1/knowledge-bases/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
    "name": "更新后的技术文档库",
    "description": "更新后的描述",
    "settings": {
        "chunk_size": 1200
    }
}
```

### 5. 删除知识库

```http
DELETE /api/v1/knowledge-bases/{id}
Authorization: Bearer <token>
```

## 文档管理API

### 1. 上传文档

```http
POST /api/v1/knowledge-bases/{id}/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <文件>
title: "API设计文档"
metadata: {"author": "张三", "version": "1.0"}
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "title": "API设计文档",
        "file_name": "api_design.pdf",
        "file_size": 1024000,
        "file_type": "application/pdf",
        "status": 0,
        "created_at": "2024-07-16T14:30:00Z"
    }
}
```

### 2. 批量上传文档

```http
POST /api/v1/knowledge-bases/{id}/documents/batch
Authorization: Bearer <token>
Content-Type: multipart/form-data

files: <多个文件>
```

### 3. 获取文档列表

```http
GET /api/v1/knowledge-bases/{id}/documents?page=1&size=10
Authorization: Bearer <token>
```

### 4. 获取文档详情

```http
GET /api/v1/documents/{id}
Authorization: Bearer <token>
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "knowledge_base_id": 1,
        "title": "API设计文档",
        "content": "文档内容...",
        "file_path": "/uploads/api_design.pdf",
        "file_size": 1024000,
        "file_type": "application/pdf",
        "metadata": {
            "author": "张三",
            "version": "1.0"
        },
        "chunks": [
            {
                "id": 1,
                "chunk_index": 0,
                "content": "第一段内容...",
                "content_length": 500
            }
        ],
        "status": 1,
        "created_at": "2024-07-16T14:30:00Z"
    }
}
```

### 5. 更新文档

```http
PUT /api/v1/documents/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
    "title": "更新后的标题",
    "metadata": {
        "author": "李四",
        "version": "1.1"
    }
}
```

### 6. 删除文档

```http
DELETE /api/v1/documents/{id}
Authorization: Bearer <token>
```

## 检索API

### 1. 语义检索

```http
POST /api/v1/knowledge-bases/{id}/search
Authorization: Bearer <token>
Content-Type: application/json

{
    "query": "如何设计RESTful API",
    "top_k": 10,
    "score_threshold": 0.7,
    "filters": {
        "file_type": ["pdf", "docx"],
        "author": "张三"
    }
}
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "query": "如何设计RESTful API",
        "results": [
            {
                "chunk_id": 1,
                "document_id": 1,
                "document_title": "API设计文档",
                "content": "RESTful API设计原则...",
                "score": 0.95,
                "metadata": {
                    "author": "张三",
                    "page": 1
                }
            }
        ],
        "total": 5,
        "search_time_ms": 150
    }
}
```

### 2. 混合检索

```http
POST /api/v1/knowledge-bases/{id}/hybrid-search
Authorization: Bearer <token>
Content-Type: application/json

{
    "query": "API设计",
    "semantic_weight": 0.7,
    "keyword_weight": 0.3,
    "top_k": 10
}
```

### 3. 关键词检索

```http
POST /api/v1/knowledge-bases/{id}/keyword-search
Authorization: Bearer <token>
Content-Type: application/json

{
    "keywords": ["API", "设计", "RESTful"],
    "operator": "AND",
    "top_k": 10
}
```

## 统计分析API

### 1. 知识库统计

```http
GET /api/v1/knowledge-bases/{id}/stats
Authorization: Bearer <token>
```

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "document_count": 25,
        "total_size": 52428800,
        "chunk_count": 1250,
        "search_count_today": 150,
        "search_count_total": 5000,
        "top_queries": [
            {
                "query": "API设计",
                "count": 50
            }
        ]
    }
}
```

### 2. 搜索历史

```http
GET /api/v1/search-logs?page=1&size=20&knowledge_base_id=1
Authorization: Bearer <token>
```

## 系统管理API

### 1. 健康检查

```http
GET /api/v1/health
```

**响应**:
```json
{
    "status": "healthy",
    "timestamp": "2024-07-16T14:30:00Z",
    "services": {
        "database": "healthy",
        "embedding": "healthy",
        "storage": "healthy"
    }
}
```

### 2. 系统信息

```http
GET /api/v1/system/info
Authorization: Bearer <token>
```

**响应**:
```json
{
    "version": "1.0.0",
    "build_time": "2024-07-16T10:00:00Z",
    "go_version": "1.21.0",
    "uptime": "2h30m15s"
}
```

## 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|-------|------|-----------|
| 0 | 成功 | 200 |
| 1001 | 参数验证失败 | 400 |
| 1002 | 请求格式错误 | 400 |
| 2001 | 认证失败 | 401 |
| 2002 | Token过期 | 401 |
| 2003 | 权限不足 | 403 |
| 3001 | 资源不存在 | 404 |
| 3002 | 资源已存在 | 409 |
| 4001 | 数据库错误 | 500 |
| 4002 | 外部服务错误 | 500 |
| 5001 | 文件上传失败 | 500 |
| 5002 | 向量化失败 | 500 |

## 限流规则

| 接口类型 | 限制 | 时间窗口 |
|---------|------|----------|
| 登录接口 | 5次/IP | 1分钟 |
| 搜索接口 | 100次/用户 | 1分钟 |
| 上传接口 | 10次/用户 | 1分钟 |
| 其他接口 | 1000次/用户 | 1分钟 |
