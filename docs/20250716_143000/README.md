# 知识库服务 (Knowledge Server)

## 项目概述

基于 Golang + Gin 框架构建的知识库服务，采用分层架构设计，支持知识库的增删改查以及向量存储和检索功能。

## 技术栈

- **后端框架**: Golang + Gin
- **数据库**: PostgreSQL + pgvector (向量扩展)
- **依赖注入**: Wire
- **向量化**: Text-Embedding
- **架构模式**: 分层架构 (API → Service → Biz → Data)

## 项目架构

```
knowledge-server/
├── cmd/                    # 应用程序入口
│   └── server/
│       └── main.go
├── internal/               # 内部包
│   ├── api/               # API层 - HTTP处理器
│   │   ├── handler/
│   │   ├── middleware/
│   │   └── router/
│   ├── service/           # Service层 - 服务编排
│   │   ├── knowledge/
│   │   └── vector/
│   ├── biz/               # Business层 - 业务逻辑
│   │   ├── knowledge/
│   │   └── vector/
│   └── data/              # Data层 - 数据访问
│       ├── database/
│       ├── repository/
│       └── model/
├── pkg/                   # 公共包
│   ├── config/
│   ├── logger/
│   ├── embedding/
│   └── utils/
├── configs/               # 配置文件
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── wire.go               # Wire依赖注入配置
├── go.mod
└── go.sum
```

## 核心功能

### 1. 知识库管理
- 创建知识库
- 查询知识库列表
- 更新知识库信息
- 删除知识库

### 2. 知识文档管理
- 上传文档到知识库
- 文档内容向量化
- 文档检索和查询
- 文档更新和删除

### 3. 向量检索
- 基于语义的相似度检索
- 混合检索（关键词 + 向量）
- 检索结果排序和过滤

## 数据库设计

### 主要表结构

#### knowledge_bases (知识库表)
```sql
CREATE TABLE knowledge_bases (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### documents (文档表)
```sql
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    knowledge_base_id INTEGER REFERENCES knowledge_bases(id),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    embedding VECTOR(1536),  -- 向量维度根据embedding模型调整
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API 设计

### 知识库相关接口

```
POST   /api/v1/knowledge-bases          # 创建知识库
GET    /api/v1/knowledge-bases          # 获取知识库列表
GET    /api/v1/knowledge-bases/:id      # 获取知识库详情
PUT    /api/v1/knowledge-bases/:id      # 更新知识库
DELETE /api/v1/knowledge-bases/:id      # 删除知识库
```

### 文档相关接口

```
POST   /api/v1/knowledge-bases/:id/documents     # 上传文档
GET    /api/v1/knowledge-bases/:id/documents     # 获取文档列表
GET    /api/v1/documents/:id                     # 获取文档详情
PUT    /api/v1/documents/:id                     # 更新文档
DELETE /api/v1/documents/:id                     # 删除文档
```

### 检索相关接口

```
POST   /api/v1/knowledge-bases/:id/search        # 语义检索
POST   /api/v1/knowledge-bases/:id/hybrid-search # 混合检索
```

## 开发环境搭建

### 前置要求
- Go 1.21+
- PostgreSQL 14+
- pgvector 扩展

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd knowledge-server
```

2. 安装依赖
```bash
go mod tidy
```

3. 配置数据库
```bash
# 安装 pgvector 扩展
CREATE EXTENSION vector;
```

4. 配置环境变量
```bash
cp configs/config.example.yaml configs/config.yaml
# 编辑配置文件
```

5. 生成依赖注入代码
```bash
wire ./...
```

6. 运行服务
```bash
go run cmd/server/main.go
```

## 配置说明

### 配置文件结构 (configs/config.yaml)

```yaml
server:
  port: 8080
  mode: debug

database:
  host: localhost
  port: 5432
  user: postgres
  password: password
  dbname: knowledge_server
  sslmode: disable

embedding:
  provider: openai  # openai, huggingface, local
  model: text-embedding-ada-002
  api_key: your-api-key
  dimension: 1536

logging:
  level: info
  format: json
```

## 部署说明

### Docker 部署

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o knowledge-server cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/knowledge-server .
COPY --from=builder /app/configs ./configs
CMD ["./knowledge-server"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  knowledge-server:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      - DB_HOST=postgres
      
  postgres:
    image: pgvector/pgvector:pg14
    environment:
      POSTGRES_DB: knowledge_server
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 测试

### 单元测试
```bash
go test ./...
```

### 集成测试
```bash
go test -tags=integration ./...
```

### API测试
```bash
# 使用提供的 Postman 集合或 curl 脚本
./scripts/test-api.sh
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
