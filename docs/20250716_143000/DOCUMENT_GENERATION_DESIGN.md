# 文档生成系统设计

## 概述

文档生成系统是知识库服务的重要组成部分，旨在自动化生成和维护技术文档。系统通过解析代码、数据库结构等信息，结合模板引擎，自动生成高质量的技术文档，并提供版本管理、增量更新等功能。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    文档生成系统                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  代码解析   │  │  数据库解析 │  │  配置解析   │         │
│  │   模块      │  │    模块     │  │    模块     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  模板引擎   │  │  生成引擎   │  │  质量检查   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  版本管理   │  │  发布系统   │  │  搜索索引   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块设计

### 1. 代码解析模块 (Code Parser)

#### 功能职责
- 解析Go源代码AST
- 提取函数、结构体、接口等元素
- 解析注释和文档字符串
- 收集类型信息和依赖关系

#### 技术实现
```go
// pkg/docgen/parser/code_parser.go
package parser

import (
    "go/ast"
    "go/parser"
    "go/token"
)

type CodeElement struct {
    Type        string            `json:"type"`         // function, struct, interface, const, var
    Name        string            `json:"name"`
    Package     string            `json:"package"`
    File        string            `json:"file"`
    Line        int               `json:"line"`
    Comment     string            `json:"comment"`
    Signature   string            `json:"signature"`
    Fields      []Field           `json:"fields,omitempty"`
    Methods     []Method          `json:"methods,omitempty"`
    Tags        map[string]string `json:"tags,omitempty"`
}

type CodeParser struct {
    fileSet *token.FileSet
    packages map[string]*ast.Package
}

func NewCodeParser() *CodeParser {
    return &CodeParser{
        fileSet:  token.NewFileSet(),
        packages: make(map[string]*ast.Package),
    }
}

func (p *CodeParser) ParseDirectory(dir string) ([]*CodeElement, error) {
    packages, err := parser.ParseDir(p.fileSet, dir, nil, parser.ParseComments)
    if err != nil {
        return nil, err
    }
    
    var elements []*CodeElement
    for _, pkg := range packages {
        pkgElements := p.parsePackage(pkg)
        elements = append(elements, pkgElements...)
    }
    
    return elements, nil
}
```

#### 解析规则
- **函数解析**: 提取函数签名、参数、返回值、注释
- **结构体解析**: 提取字段、标签、方法、嵌入类型
- **接口解析**: 提取方法定义和约束
- **注释解析**: 支持标准Go文档注释格式

### 2. 数据库解析模块 (Database Parser)

#### 功能职责
- 连接PostgreSQL数据库
- 解析表结构和约束
- 提取字段注释和类型信息
- 生成数据字典

#### 技术实现
```go
// pkg/docgen/parser/db_parser.go
package parser

type TableInfo struct {
    Schema      string      `json:"schema"`
    Name        string      `json:"name"`
    Comment     string      `json:"comment"`
    Columns     []Column    `json:"columns"`
    Indexes     []Index     `json:"indexes"`
    Constraints []Constraint `json:"constraints"`
}

type Column struct {
    Name         string `json:"name"`
    Type         string `json:"type"`
    Nullable     bool   `json:"nullable"`
    Default      string `json:"default"`
    Comment      string `json:"comment"`
    IsPrimaryKey bool   `json:"is_primary_key"`
    IsForeignKey bool   `json:"is_foreign_key"`
}

type DatabaseParser struct {
    db *sql.DB
}

func (p *DatabaseParser) ParseSchema(schema string) ([]*TableInfo, error) {
    query := `
        SELECT 
            t.table_name,
            obj_description(c.oid) as table_comment
        FROM information_schema.tables t
        LEFT JOIN pg_class c ON c.relname = t.table_name
        WHERE t.table_schema = $1
        ORDER BY t.table_name
    `
    
    rows, err := p.db.Query(query, schema)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var tables []*TableInfo
    for rows.Next() {
        var table TableInfo
        err := rows.Scan(&table.Name, &table.Comment)
        if err != nil {
            continue
        }
        
        table.Schema = schema
        table.Columns = p.parseColumns(schema, table.Name)
        table.Indexes = p.parseIndexes(schema, table.Name)
        table.Constraints = p.parseConstraints(schema, table.Name)
        
        tables = append(tables, &table)
    }
    
    return tables, nil
}
```

### 3. 模板引擎 (Template Engine)

#### 功能职责
- 管理文档模板
- 渲染文档内容
- 支持多种输出格式
- 提供模板继承和组合

#### 技术实现
```go
// pkg/docgen/template/engine.go
package template

import (
    "text/template"
    "html/template"
)

type TemplateEngine struct {
    templates map[string]*template.Template
    funcs     template.FuncMap
}

type TemplateData struct {
    Title       string                 `json:"title"`
    Description string                 `json:"description"`
    Version     string                 `json:"version"`
    GeneratedAt time.Time              `json:"generated_at"`
    Data        map[string]interface{} `json:"data"`
}

func NewTemplateEngine() *TemplateEngine {
    return &TemplateEngine{
        templates: make(map[string]*template.Template),
        funcs:     defaultFuncMap(),
    }
}

func (e *TemplateEngine) LoadTemplate(name, content string) error {
    tmpl, err := template.New(name).Funcs(e.funcs).Parse(content)
    if err != nil {
        return err
    }
    
    e.templates[name] = tmpl
    return nil
}

func (e *TemplateEngine) Render(templateName string, data *TemplateData) (string, error) {
    tmpl, exists := e.templates[templateName]
    if !exists {
        return "", fmt.Errorf("template %s not found", templateName)
    }
    
    var buf bytes.Buffer
    err := tmpl.Execute(&buf, data)
    if err != nil {
        return "", err
    }
    
    return buf.String(), nil
}
```

#### 模板类型
- **API文档模板**: OpenAPI/Swagger格式
- **数据库文档模板**: 数据字典格式
- **架构文档模板**: 系统架构说明
- **用户手册模板**: 使用指南格式

### 4. 生成引擎 (Generation Engine)

#### 功能职责
- 协调各个解析模块
- 执行文档生成流程
- 管理生成任务队列
- 处理并发生成

#### 技术实现
```go
// pkg/docgen/engine/generator.go
package engine

type GenerationConfig struct {
    ProjectPath    string            `yaml:"project_path"`
    OutputPath     string            `yaml:"output_path"`
    Templates      map[string]string `yaml:"templates"`
    Formats        []string          `yaml:"formats"`
    IncludePrivate bool              `yaml:"include_private"`
    Languages      []string          `yaml:"languages"`
}

type Generator struct {
    config       *GenerationConfig
    codeParser   *parser.CodeParser
    dbParser     *parser.DatabaseParser
    templateEng  *template.TemplateEngine
    versionMgr   *version.Manager
}

func (g *Generator) Generate(ctx context.Context) error {
    // 1. 解析代码
    codeElements, err := g.codeParser.ParseDirectory(g.config.ProjectPath)
    if err != nil {
        return fmt.Errorf("code parsing failed: %w", err)
    }
    
    // 2. 解析数据库
    dbTables, err := g.dbParser.ParseSchema("public")
    if err != nil {
        return fmt.Errorf("database parsing failed: %w", err)
    }
    
    // 3. 生成文档
    docs := g.generateDocuments(codeElements, dbTables)
    
    // 4. 保存文档
    for _, doc := range docs {
        err := g.saveDocument(doc)
        if err != nil {
            return fmt.Errorf("save document failed: %w", err)
        }
    }
    
    // 5. 更新版本
    return g.versionMgr.CreateVersion(docs)
}
```

### 5. 版本管理模块 (Version Manager)

#### 功能职责
- 跟踪文档变更
- 管理文档版本
- 提供差异对比
- 支持版本回滚

#### 技术实现
```go
// pkg/docgen/version/manager.go
package version

type DocumentVersion struct {
    ID          string    `json:"id"`
    DocumentID  string    `json:"document_id"`
    Version     string    `json:"version"`
    Content     string    `json:"content"`
    Hash        string    `json:"hash"`
    CreatedAt   time.Time `json:"created_at"`
    CreatedBy   string    `json:"created_by"`
    ChangeLog   string    `json:"change_log"`
}

type VersionManager struct {
    repo repository.VersionRepository
}

func (m *VersionManager) CreateVersion(docs []*Document) error {
    for _, doc := range docs {
        // 计算内容哈希
        hash := m.calculateHash(doc.Content)
        
        // 检查是否有变更
        lastVersion, err := m.repo.GetLatestVersion(doc.ID)
        if err == nil && lastVersion.Hash == hash {
            continue // 无变更，跳过
        }
        
        // 创建新版本
        version := &DocumentVersion{
            ID:         uuid.New().String(),
            DocumentID: doc.ID,
            Version:    m.generateVersionNumber(doc.ID),
            Content:    doc.Content,
            Hash:       hash,
            CreatedAt:  time.Now(),
            ChangeLog:  m.generateChangeLog(lastVersion, doc),
        }
        
        err = m.repo.SaveVersion(version)
        if err != nil {
            return err
        }
    }
    
    return nil
}
```

## 文档类型定义

### 1. API文档
```yaml
# templates/api.yaml
type: api_documentation
format: openapi_3.0
sections:
  - overview
  - authentication
  - endpoints
  - models
  - examples
  - errors
```

### 2. 数据库文档
```yaml
# templates/database.yaml
type: database_documentation
format: markdown
sections:
  - overview
  - tables
  - relationships
  - indexes
  - constraints
  - migrations
```

### 3. 架构文档
```yaml
# templates/architecture.yaml
type: architecture_documentation
format: markdown
sections:
  - overview
  - components
  - data_flow
  - deployment
  - security
  - performance
```

## 配置管理

### 1. 生成配置
```yaml
# docgen.yaml
project:
  name: "Knowledge Server"
  version: "1.0.0"
  description: "企业级知识库服务"

source:
  code_paths:
    - "internal/"
    - "pkg/"
  exclude_paths:
    - "internal/test/"
    - "pkg/mock/"
  database:
    host: "localhost"
    port: 5432
    database: "knowledge_server"
    schema: "public"

output:
  path: "docs/generated/"
  formats: ["markdown", "html", "pdf"]
  languages: ["zh-CN", "en-US"]

templates:
  api: "templates/api.md.tmpl"
  database: "templates/database.md.tmpl"
  architecture: "templates/architecture.md.tmpl"

generation:
  include_private: false
  include_tests: false
  auto_update: true
  schedule: "0 2 * * *"  # 每天凌晨2点自动生成
```

### 2. 模板配置
```yaml
# templates/config.yaml
templates:
  api:
    name: "API Documentation"
    description: "RESTful API接口文档"
    output_file: "api.md"
    sections:
      - name: "overview"
        required: true
      - name: "endpoints"
        required: true
      - name: "models"
        required: false

  database:
    name: "Database Documentation"
    description: "数据库设计文档"
    output_file: "database.md"
    sections:
      - name: "tables"
        required: true
      - name: "relationships"
        required: true
```

## 集成方案

### 1. 与现有系统集成
```go
// internal/service/docgen/service.go
package docgen

type Service struct {
    generator    *engine.Generator
    knowledgeBiz knowledge.Biz
    logger       logger.Logger
}

func (s *Service) GenerateDocuments(ctx context.Context, req *GenerateRequest) error {
    // 1. 生成文档
    docs, err := s.generator.Generate(ctx)
    if err != nil {
        return err
    }
    
    // 2. 保存到知识库
    for _, doc := range docs {
        kbDoc := &knowledge.Document{
            Title:   doc.Title,
            Content: doc.Content,
            Type:    "generated",
        }
        
        err := s.knowledgeBiz.CreateDocument(ctx, kbDoc)
        if err != nil {
            s.logger.Error("保存生成文档失败", "error", err)
        }
    }
    
    return nil
}
```

### 2. API接口设计
```go
// internal/api/handler/docgen.go
package handler

// 生成文档
// @Summary 生成项目文档
// @Description 根据代码和数据库结构自动生成技术文档
// @Tags 文档生成
// @Accept json
// @Produce json
// @Param request body GenerateRequest true "生成请求"
// @Success 200 {object} GenerateResponse
// @Router /api/v1/docgen/generate [post]
func (h *DocGenHandler) Generate(c *gin.Context) {
    var req GenerateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    err := h.service.GenerateDocuments(c.Request.Context(), &req)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, gin.H{"message": "文档生成成功"})
}
```

## 部署和运维

### 1. 定时任务
```yaml
# k8s/cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: docgen-scheduler
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: docgen
            image: knowledge-server:latest
            command: ["./knowledge-server", "docgen", "generate"]
            env:
            - name: CONFIG_PATH
              value: "/config/docgen.yaml"
          restartPolicy: OnFailure
```

### 2. 监控指标
```go
// pkg/docgen/metrics/metrics.go
package metrics

var (
    GenerationDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "docgen_generation_duration_seconds",
            Help: "文档生成耗时",
        },
        []string{"type"},
    )
    
    GenerationErrors = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "docgen_generation_errors_total",
            Help: "文档生成错误数",
        },
        []string{"type", "error"},
    )
)
```

## 扩展功能

### 1. 多语言支持
- 国际化模板
- 自动翻译集成
- 多语言版本管理

### 2. 协作功能
- 文档评审流程
- 协作编辑
- 评论和反馈

### 3. 智能优化
- AI辅助文档生成
- 内容质量评估
- 自动化改进建议

这个设计为文档生成系统提供了完整的架构方案，涵盖了从代码解析到文档发布的全流程，确保系统的可扩展性和可维护性。
